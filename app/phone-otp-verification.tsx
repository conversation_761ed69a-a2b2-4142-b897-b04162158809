import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
    Image,
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    StatusBar,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

import { useLanguage } from '@/hooks/useLanguage';

export default function PhoneOtpVerification() {
  const { t } = useLanguage();
  const params = useLocalSearchParams();
  const phoneNumber = params.phoneNumber as string;
  const countryCode = params.countryCode as string;
  
  const [otp, setOtp] = useState(['', '', '', '']);
  const inputRefs = useRef<(TextInput | null)[]>([]);

  const handleOtpChange = (value: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = () => {
    const otpCode = otp.join('');
    if (otpCode.length === 4) {
      // Handle OTP verification logic here
      console.log('Verifying OTP:', otpCode);
      // Navigate to success modal
      router.push('/verification-success' as any);
    }
  };

  const handleResendOtp = () => {
    // Handle resend OTP logic here
    console.log('Resending OTP to:', countryCode + phoneNumber);
    setOtp(['', '', '', '']);
    inputRefs.current[0]?.focus();
  };

  const handleEditNumber = () => {
    router.back();
  };

  const renderKeypadButton = (label: string, onPress: () => void, isSpecial = false, subLabel?: string) => (
    <TouchableOpacity
      onPress={onPress}
      className={`items-center justify-center w-20 h-16 rounded-lg ${
        isSpecial ? 'bg-gray-200' : 'bg-white'
      }`}
      style={{ elevation: 2, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.1, shadowRadius: 2 }}
    >
      {label === 'backspace' ? (
        <Ionicons name="backspace-outline" size={24} color="#374151" />
      ) : (
        <View className="items-center">
          <Text className="text-xl text-gray-900 font-cairo-medium">{label}</Text>
          {subLabel && (
            <Text className="text-xs text-gray-500 font-cairo-regular">{subLabel}</Text>
          )}
        </View>
      )}
    </TouchableOpacity>
  );

  const handleKeypadPress = (value: string) => {
    if (value === 'backspace') {
      const currentIndex = otp.findIndex(digit => digit === '');
      const indexToUpdate = currentIndex === -1 ? 3 : Math.max(0, currentIndex - 1);
      
      const newOtp = [...otp];
      newOtp[indexToUpdate] = '';
      setOtp(newOtp);
      
      inputRefs.current[indexToUpdate]?.focus();
    } else {
      const currentIndex = otp.findIndex(digit => digit === '');
      if (currentIndex !== -1) {
        handleOtpChange(value, currentIndex);
      }
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View className="flex-row items-center justify-between px-6 py-4">
          <TouchableOpacity onPress={() => router.back()} className="p-2">
            <Ionicons name="arrow-back" size={24} color="#374151" />
          </TouchableOpacity>
        </View>

        {/* Logo */}
        <View className="items-center px-6 pb-8">
          <Image
            source={require('@/assets/images/logo.png')}
            className="h-16 w-48"
            resizeMode="contain"
          />
        </View>

        {/* Content */}
        <View className="flex-1 px-6">
          {/* Title */}
          <Text className="mb-4 text-2xl text-gray-900 font-cairo-bold">
            {t('auth.verification.phoneOtp.title')}
          </Text>

          {/* Subtitle with phone number */}
          <View className="mb-8">
            <Text className="mb-2 text-base text-gray-600 font-cairo-regular">
              {t('auth.verification.phoneOtp.subtitle')}
            </Text>
            <View className="flex-row items-center">
              <Text className="text-base text-gray-900 font-cairo-semibold">
                {countryCode}{phoneNumber}
              </Text>
              <TouchableOpacity onPress={handleEditNumber} className="ml-2">
                <Ionicons name="pencil" size={16} color="#F59E0B" />
              </TouchableOpacity>
            </View>
          </View>

          {/* OTP Input Fields */}
          <View className="flex-row justify-center mb-8 space-x-4">
            {otp.map((digit, index) => (
              <View key={index} className="relative">
                <TextInput
                  ref={(ref) => (inputRefs.current[index] = ref)}
                  value={digit}
                  onChangeText={(value) => handleOtpChange(value.slice(-1), index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  className={`w-14 h-14 text-center text-xl text-gray-900 border-2 rounded-lg font-cairo-semibold ${
                    digit ? 'border-primary bg-yellow-50' : 'border-gray-300 bg-gray-50'
                  }`}
                  keyboardType="numeric"
                  maxLength={1}
                  selectTextOnFocus
                />
              </View>
            ))}
          </View>

          {/* Resend OTP */}
          <View className="items-center mb-8">
            <Text className="mb-2 text-sm text-gray-500 font-cairo-regular">
              {t('auth.verification.phoneOtp.haventReceived')}
            </Text>
            <TouchableOpacity onPress={handleResendOtp}>
              <Text className="text-sm text-primary font-cairo-medium">
                {t('auth.verification.phoneOtp.resendOtp')}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Verify Button */}
          <TouchableOpacity
            onPress={handleVerify}
            disabled={otp.some(digit => !digit)}
            className={`items-center justify-center w-full mb-8 rounded-lg h-14 ${
              otp.some(digit => !digit) ? 'bg-gray-300' : 'bg-primary'
            }`}
          >
            <Text className={`text-lg font-cairo-semibold ${
              otp.some(digit => !digit) ? 'text-gray-500' : 'text-black'
            }`}>
              {t('auth.verification.phoneOtp.verify')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Custom Keypad */}
        <View className="px-6 pb-8">
          <View className="flex-row justify-center space-x-4 mb-4">
            {renderKeypadButton('1', () => handleKeypadPress('1'))}
            {renderKeypadButton('2', () => handleKeypadPress('2'), false, 'ABC')}
            {renderKeypadButton('3', () => handleKeypadPress('3'), false, 'DEF')}
          </View>
          <View className="flex-row justify-center space-x-4 mb-4">
            {renderKeypadButton('4', () => handleKeypadPress('4'), false, 'GHI')}
            {renderKeypadButton('5', () => handleKeypadPress('5'), false, 'JKL')}
            {renderKeypadButton('6', () => handleKeypadPress('6'), false, 'MNO')}
          </View>
          <View className="flex-row justify-center space-x-4 mb-4">
            {renderKeypadButton('7', () => handleKeypadPress('7'), false, 'PQRS')}
            {renderKeypadButton('8', () => handleKeypadPress('8'), false, 'TUV')}
            {renderKeypadButton('9', () => handleKeypadPress('9'), false, 'WXYZ')}
          </View>
          <View className="flex-row justify-center space-x-4 mb-4">
            {renderKeypadButton('+ * #', () => {}, true)}
            {renderKeypadButton('0', () => handleKeypadPress('0'))}
            {renderKeypadButton('backspace', () => handleKeypadPress('backspace'), true)}
          </View>
        </View>

        {/* Bottom Indicator */}
        <View className="items-center pb-8">
          <View className="w-32 h-1 bg-black rounded-full" />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
