import { Ionicons } from '@expo/vector-icons';
import { Link, router } from 'expo-router';
import React, { useState } from 'react';
import {
  FlatList,
  Image,
  KeyboardAvoidingView,
  Modal,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import { COUNTRIES, DEFAULT_COUNTRY, type Country } from '@/constants/Countries';
import { useLanguage } from '@/hooks/useLanguage';

export default function SignUp() {
  const { t } = useLanguage();
  const [userType, setUserType] = useState<'car-owner' | 'transporter'>('car-owner');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<Country>(DEFAULT_COUNTRY);
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [mobileNumber, setMobileNumber] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [acceptNotifications, setAcceptNotifications] = useState(false);

  const handleSignUp = () => {
    // Handle sign up logic here
    console.log('Sign up with:', {
      userType,
      firstName,
      lastName,
      email,
      countryCode: selectedCountry.dialCode,
      mobileNumber,
      password,
      confirmPassword,
      acceptTerms,
      acceptNotifications,
    });
    // Navigate to main app or verification screen
    router.replace('/(tabs)');
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
        >
          <View className="flex-1 px-6 pt-16">
            {/* Logo */}
            <View className="items-center mb-8">
              <Image
                source={require('../assets/images/logo.png')}
                className="w-40 h-16"
                resizeMode="contain"
              />
            </View>

            {/* Create Account Text */}
            <View className="mb-6">
              <Text className="mb-2 text-2xl text-gray-900 font-cairo-bold">
                {t('auth.signup.title')}
              </Text>
              <Text className="text-base text-gray-500 font-cairo-regular">
                {t('auth.signup.subtitle')}
              </Text>
            </View>

            {/* User Type Selection */}
            <View className="flex-row gap-4 mb-6">
              {/* Car Owner */}
              <TouchableOpacity
                onPress={() => setUserType('car-owner')}
                className={`flex-1 h-24 rounded-lg items-center justify-center ${
                  userType === 'car-owner' ? 'bg-primary' : 'bg-gray-100'
                }`}
              >
                <Image
                  source={require('../assets/images/auth/car-icon.png')}
                  className="w-8 h-8 mb-2"
                  resizeMode="contain"
                />
                <Text
                  className={`font-cairo-semibold ${
                    userType === 'car-owner' ? 'text-black' : 'text-gray-600'
                  }`}
                >
                  {t('auth.signup.carOwner')}
                </Text>
              </TouchableOpacity>

              {/* Transporter */}
              <TouchableOpacity
                onPress={() => setUserType('transporter')}
                className={`flex-1 h-24 rounded-lg items-center justify-center ${
                  userType === 'transporter' ? 'bg-primary' : 'bg-gray-100'
                }`}
              >
                <Image
                  source={require('../assets/images/auth/truck-icon.png')}
                  className="w-8 h-8 mb-2"
                  resizeMode="contain"
                />
                <Text
                  className={`font-cairo-semibold ${
                    userType === 'transporter' ? 'text-black' : 'text-gray-600'
                  }`}
                >
                  {t('auth.signup.transporter')}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Name Inputs */}
            <View className="flex-row gap-4 mb-4">
              <TextInput
                value={firstName}
                onChangeText={setFirstName}
                placeholder={t('auth.signup.firstName')}
                placeholderTextColor="#9CA3AF"
                className="flex-1 px-4 text-base text-gray-900 rounded-lg h-14 bg-gray-50 font-cairo-regular"
                autoCapitalize="words"
              />
              <TextInput
                value={lastName}
                onChangeText={setLastName}
                placeholder={t('auth.signup.lastName')}
                placeholderTextColor="#9CA3AF"
                className="flex-1 px-4 text-base text-gray-900 rounded-lg h-14 bg-gray-50 font-cairo-regular"
                autoCapitalize="words"
              />
            </View>

            {/* Email Input */}
            <View className="mb-4">
              <TextInput
                value={email}
                onChangeText={setEmail}
                placeholder={t('auth.signup.email')}
                placeholderTextColor="#9CA3AF"
                className="w-full px-4 text-base text-gray-900 rounded-lg h-14 bg-gray-50 font-cairo-regular"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
              />
            </View>

            {/* Mobile Number Input */}
            <View className="flex-row gap-4 mb-4">
              <TouchableOpacity
                onPress={() => setShowCountryModal(true)}
                className="flex-row items-center min-w-[100px] px-3 rounded-lg bg-gray-50 h-14"
              >
                <Text className="mr-1 text-gray-700 font-cairo-regular">{selectedCountry.flag}</Text>
                <Text className="mr-1 text-gray-700 font-cairo-regular">{selectedCountry.dialCode}</Text>
                <Ionicons name="chevron-down" size={16} color="#9CA3AF" />
              </TouchableOpacity>
              <TextInput
                value={mobileNumber}
                onChangeText={setMobileNumber}
                placeholder={t('auth.signup.mobileNumber')}
                placeholderTextColor="#9CA3AF"
                className="flex-1 px-4 text-base text-gray-900 rounded-lg h-14 bg-gray-50 font-cairo-regular"
                keyboardType="phone-pad"
              />
            </View>

            {/* Password Input */}
            <View className="relative mb-4">
              <TextInput
                value={password}
                onChangeText={setPassword}
                placeholder={t('auth.signup.password')}
                placeholderTextColor="#9CA3AF"
                className="w-full px-4 pr-12 text-base text-gray-900 rounded-lg h-14 bg-gray-50 font-cairo-regular"
                secureTextEntry={!showPassword}
                autoComplete="new-password"
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-4"
              >
                <Ionicons
                  name={showPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color="#9CA3AF"
                />
              </TouchableOpacity>
            </View>

            {/* Confirm Password Input */}
            <View className="relative mb-6">
              <TextInput
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder={t('auth.signup.confirmPassword')}
                placeholderTextColor="#9CA3AF"
                className="w-full px-4 pr-12 text-base text-gray-900 rounded-lg h-14 bg-gray-50 font-cairo-regular"
                secureTextEntry={!showConfirmPassword}
                autoComplete="new-password"
              />
              <TouchableOpacity
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-4 top-4"
              >
                <Ionicons
                  name={showConfirmPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color="#9CA3AF"
                />
              </TouchableOpacity>
            </View>

            {/* Checkboxes */}
            <View className="mb-8">
              {/* Accept Terms */}
              <TouchableOpacity
                onPress={() => setAcceptTerms(!acceptTerms)}
                className="flex-row items-center mb-4"
              >
                <View
                  className={`w-5 h-5 rounded border-2 mr-3 items-center justify-center ${
                    acceptTerms
                      ? 'bg-primary border-primary'
                      : 'bg-white border-gray-300'
                  }`}
                >
                  {acceptTerms && (
                    <Ionicons name="checkmark" size={12} color="black" />
                  )}
                </View>
                <Text className="flex-1 text-gray-700 font-cairo-regular">
                  {t('auth.signup.acceptTerms')}
                </Text>
              </TouchableOpacity>

              {/* Accept Notifications */}
              <TouchableOpacity
                onPress={() => setAcceptNotifications(!acceptNotifications)}
                className="flex-row items-center"
              >
                <View
                  className={`w-5 h-5 rounded border-2 mr-3 items-center justify-center ${
                    acceptNotifications
                      ? 'bg-primary border-primary'
                      : 'bg-white border-gray-300'
                  }`}
                >
                  {acceptNotifications && (
                    <Ionicons name="checkmark" size={12} color="black" />
                  )}
                </View>
                <Text className="flex-1 text-gray-700 font-cairo-regular">
                  {t('auth.signup.acceptNotifications')}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Sign Up Button */}
            <TouchableOpacity
              onPress={handleSignUp}
              className="items-center justify-center w-full mb-6 rounded-lg h-14 bg-primary"
            >
              <Text className="text-lg text-black font-cairo-semibold">
                {t('auth.signup.signUpButton')}
              </Text>
            </TouchableOpacity>

            {/* Sign In Link */}
            <View className="items-center mb-6">
              <Text className="text-gray-500 font-cairo-regular">
                {t('auth.signup.alreadyHaveAccount')}{' '}
                <Link href="/signin" asChild>
                  <Text className="text-primary font-cairo-medium">
                    {t('auth.signup.signIn')}
                  </Text>
                </Link>
              </Text>
            </View>
          </View>

          {/* Bottom Indicator */}
          <View className="items-center pb-8">
            <View className="w-32 h-1 bg-black rounded-full" />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Countries Modal */}
      <Modal
        visible={showCountryModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCountryModal(false)}
      >
        <SafeAreaView className="flex-1 bg-white">
          <View className="flex-1">
            {/* Modal Header */}
            <View className="flex-row items-center justify-between p-4 border-b border-gray-200">
              <Text className="text-lg text-gray-900 font-cairo-semibold">
                Select Country
              </Text>
              <TouchableOpacity
                onPress={() => setShowCountryModal(false)}
                className="p-2"
              >
                <Ionicons name="close" size={24} color="#374151" />
              </TouchableOpacity>
            </View>

            {/* Countries List */}
            <FlatList
              data={COUNTRIES}
              keyExtractor={(item) => item.code}
              renderItem={({ item }) => (
                <TouchableOpacity
                  onPress={() => {
                    setSelectedCountry(item);
                    setShowCountryModal(false);
                  }}
                  className="flex-row items-center p-4 border-b border-gray-100"
                >
                  <Text className="mr-3 text-2xl">{item.flag}</Text>
                  <View className="flex-1">
                    <Text className="text-gray-900 font-cairo-medium">
                      {t(item.name)}
                    </Text>
                    <Text className="text-sm text-gray-500 font-cairo-regular">
                      {item.dialCode}
                    </Text>
                  </View>
                  {selectedCountry.code === item.code && (
                    <Ionicons name="checkmark" size={20} color="#FFC700" />
                  )}
                </TouchableOpacity>
              )}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}
